Config = {}

-- 挂机区域配置
Config.HangAreas = {
    {
        coords = vector3(968.42, -1015.54, 40.84), 
        radius = 50.0, 
        name = "挂机区",
        blipSprite = 162,    
        blipColor = 2,
        blipVisible = true,
        rewardInterval = 5000, -- 每5000毫秒（5秒）发放奖励
        rewardItems = {          -- 该挂机区的奖励物品及其获得概率
            {name = "money", count = 1, chance = 80},     -- 80% 概率
            {name = "black_money", count = 2, chance = 100} -- 100% 概率
        }
    },
    {
        coords = vector3(217.71, -811.80, 30.66), 
        radius = 5.0, 
        name = "挂机区",
        blipSprite = 162,
        blipColor = 3,
        blipVisible = false,
        rewardInterval = 3000, -- 每3000毫秒（3秒）发放奖励
        rewardItems = {          -- 该挂机区的奖励物品及其获得概率
            {name = "money", count = 2, chance = 100}     -- 100% 概率
        }
    }
}

-- VIP配置
Config.VIPMultiplier = 4 -- VIP奖励倍数

-- VIP状态检查间隔
Config.VIPCheckInterval = 10000 -- 10秒检查一次

-- 调试设置
Config.Debug = {
    Enabled = true,  -- 是否启用调试显示
    ShowStatusValues = true,  -- 是否显示饱食度和口渴值
    UpdateInterval = 1000  -- 调试信息更新间隔（毫秒）
}

-- 恢复设置
Config.RecoverySettings = {
    Interval = 10000,  -- 恢复间隔（毫秒）- 每10秒恢复一次
    Health = {
        Enabled = true,
        Amount = 1,
        MaxValue = 200
    },
    Armor = {
        Enabled = true,
        Amount = 1,
        MaxValue = 100
    },
    Hunger = {
        Enabled = true,
        Amount = 1,  -- 每次恢复1点饱食度（更温和）
        MaxValue = 100
    },
    Thirst = {
        Enabled = true,
        Amount = 1,  -- 每次恢复1点口渴值（更温和）
        MaxValue = 100
    }
}
