local ESX = exports['es_extended']:getSharedObject()

-- 玩家奖励次数追踪
local playerRewardCount = {}
local lastRewardTime = {}

-- VIP 检查回调
ESX.RegisterServerCallback('esx_hangarea:checkVIP', function(source, cb)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then 
        cb(false)
        return 
    end

    -- print("开始检查 VIP 状态 - Identifier: " .. xPlayer.identifier) -- 打印检查的 Identifier

    MySQL.Async.fetchScalar('SELECT COUNT(*) as vip_count FROM user_vip WHERE identifier = @identifier', {
        ['@identifier'] = xPlayer.identifier
    }, function(vipCount)
        local isVIP = vipCount and vipCount > 0
        
        -- print(string.format("VIP 检查结果 - 玩家: %s, VIP状态: %s, 记录数: %d", 
        --     xPlayer.getName(), 
        --     isVIP and "是" or "否",
        --     vipCount or 0
        -- ))

        cb(isVIP)
    end)
end)

-- 发放奖励
-- 发放奖励
RegisterServerEvent('esx_hangarea:requestReward')
AddEventHandler('esx_hangarea:requestReward', function(areaName)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)
    
    if not xPlayer then 
        return 
    end

    local currentTime = os.time()
    local areaConfig = nil

    -- 查找区域配置
    for _, area in ipairs(Config.HangAreas) do
        if area.name == areaName then
            areaConfig = area
            break
        end
    end

    if not areaConfig then 
        return 
    end

    local rewardIntervalSeconds = areaConfig.rewardInterval / 1000

    -- 检查奖励冷却时间
    if lastRewardTime[_source] and (currentTime - lastRewardTime[_source]) < rewardIntervalSeconds then
        return 
    end

    -- 检查VIP状态
    MySQL.Async.fetchScalar('SELECT COUNT(*) as vip_count FROM user_vip WHERE identifier = @identifier', {
        ['@identifier'] = xPlayer.identifier
    }, function(vipCount)
        local isVIP = vipCount and vipCount > 0
        local multiplier = isVIP and Config.VIPMultiplier or 1

        -- 发放奖励
        for _, item in ipairs(areaConfig.rewardItems) do
            -- 根据概率判断是否发放物品
            local randomChance = math.random(1, 100) -- 生成1到100的随机数
            if randomChance <= item.chance then
                local rewardCount = math.floor(item.count * multiplier)
                if xPlayer.canCarryItem(item.name, rewardCount) then
                    xPlayer.addInventoryItem(item.name, rewardCount)
                else
                    -- 如果背包已满
                    TriggerClientEvent('esx:showNotification', _source, '背包已满，无法获得奖励')
                    return
                end
            end
        end

        -- 更新奖励次数和时间
        playerRewardCount[_source] = playerRewardCount[_source] or 0
        playerRewardCount[_source] = playerRewardCount[_source] + 1
        lastRewardTime[_source] = currentTime
        
        -- 通知客户端奖励次数
        TriggerClientEvent('esx_hangarea:updateRewardCount', _source, playerRewardCount[_source])
        
        -- 通知玩家
        local rewardMessage = isVIP and 
            string.format('你在%s获得了VIP挂机奖励(%.1f倍)!', areaName, multiplier) or 
            string.format('你在%s获得了挂机奖励!', areaName)
        
        TriggerClientEvent('esx:showNotification', _source, rewardMessage)
    end)
end)


function HandleReward(xPlayer, areaConfig, currentTime)
    local rewardIntervalSeconds = areaConfig.rewardInterval / 1000

    -- 检查玩家上次奖励时间
    if lastRewardTime[source] and (currentTime - lastRewardTime[source]) < rewardIntervalSeconds then
        return -- 如果未到达奖励时间，返回
    end

    -- 初始化玩家奖励次数
    playerRewardCount[source] = playerRewardCount[source] or 0

    -- 发放奖励逻辑
    MySQL.Async.fetchScalar('SELECT COUNT(*) as vip_count FROM user_vip WHERE identifier = @identifier', {
        ['@identifier'] = xPlayer.identifier
    }, function(vipCount)
        local isVIP = vipCount and vipCount > 0
        local multiplier = isVIP and Config.VIPMultiplier or 1

        -- 使用当前区域的奖励
        for _, item in ipairs(areaConfig.rewards) do
            local rewardCount = math.floor(item.count * multiplier)
            if xPlayer.canCarryItem(item.name, rewardCount) then
                xPlayer.addInventoryItem(item.name, rewardCount)
            else
                -- 如果背包已满
                TriggerClientEvent('esx:showNotification', source, '背包已满，无法获得奖励')
                return
            end
        end

        -- 更新奖励次数和时间
        playerRewardCount[source] = playerRewardCount[source] + 1
        lastRewardTime[source] = currentTime
        
        -- 通知客户端奖励次数
        TriggerClientEvent('esx_hangarea:updateRewardCount', source, playerRewardCount[source])
        
        -- 通知玩家
        local rewardMessage = isVIP and 
            string.format('你在%s获得了VIP挂机奖励(%.1f倍)!', areaConfig.name, multiplier) or 
            string.format('你在%s获得了挂机奖励!', areaConfig.name)
        
        TriggerClientEvent('esx:showNotification', source, rewardMessage)
    end)
end




-- 获取玩家 Identifier 命令
-- RegisterCommand('getidentifier', function(source, args, rawCommand)
--     local xPlayer = ESX.GetPlayerFromId(source)
--     if xPlayer then
--         print('玩家 ' .. xPlayer.getName() .. ' 的 Identifier: ' .. xPlayer.identifier)
--         TriggerClientEvent('esx:showNotification', source, '你的 Identifier 已打印到服务器控制台')
--     end
-- end)

-- 添加 VIP 命令
RegisterCommand('addvip', function(source, args, rawCommand)
    if source == 0 or IsPlayerAceAllowed(source, 'command.addvip') then
        if #args < 1 then
            -- print('用法: addvip <identifier>')
            return
        end
        
        local identifier = args[1]
        print("添加 VIP 的 Identifier: " .. identifier) -- 打印添加的 Identifier
        
        MySQL.Async.execute('REPLACE INTO user_vip (identifier) VALUES (@identifier)', {
            ['@identifier'] = identifier
        }, function(rowsChanged)
            if rowsChanged > 0 then
                print('成功为 ' .. identifier .. ' 添加 VIP')
                Citizen.Wait(100) -- 等待以确保数据被提交
                
                -- 额外验证
                MySQL.Async.fetchScalar('SELECT COUNT(*) FROM user_vip WHERE identifier = @identifier', {
                    ['@identifier'] = identifier
                }, function(count)
                    print(string.format("验证 VIP 添加结果 - 记录数: %d", count or 0))
                end)
            else
                print('添加 VIP 失败')
                if source > 0 then
                    TriggerClientEvent('esx:showNotification', source, 'VIP 添加失败')
                end
            end
        end)
    end
end)


-- 移除 VIP 命令
RegisterCommand('removevip', function(source, args, rawCommand)
    if source == 0 or IsPlayerAceAllowed(source, 'command.removevip') then
        if #args < 1 then
            print('用法: removevip <identifier>')
            return
        end
        
        local identifier = args[1]
        
        MySQL.Async.execute('DELETE FROM user_vip WHERE identifier = @identifier', {
            ['@identifier'] = identifier
        }, function(rowsChanged)
            if rowsChanged > 0 then
                print('成功移除 ' .. identifier .. ' 的 VIP')
                if source > 0 then
                    TriggerClientEvent('esx:showNotification', source, '成功移除 VIP')
                end
            else
                print('移除 VIP 失败，可能不存在')
                if source > 0 then
                    TriggerClientEvent('esx:showNotification', source, 'VIP 移除失败')
                end
            end
        end)
    end
end)

-- 添加 VIP 调试命令
RegisterCommand('debugvip', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return end

    -- print("===== VIP 调试信息 =====")
    -- print("玩家名称: " .. xPlayer.getName())
    -- print("玩家 Identifier: " .. xPlayer.identifier)

    MySQL.Async.fetchAll('SELECT * FROM user_vip WHERE identifier = @identifier', {
        ['@identifier'] = xPlayer.identifier
    }, function(result)
        print("数据库查询结果数量: " .. #result)
        if #result > 0 then
            print("VIP 信息:")
            for k, v in pairs(result[1]) do
                print(k .. ": " .. tostring(v))
            end
            print("玩家是 VIP")
        else
            print("玩家不是 VIP")
        end
    end)
end)

-- 检查 VIP 状态（同步方法）
function CheckVIPStatus(identifier)
    local result = MySQL.Sync.fetchScalar("SELECT COUNT(*) FROM user_vip WHERE identifier = @identifier", {
        ['@identifier'] = identifier
    })
    
    return result and result > 0 -- 如果存在记录，则是 VIP
end

-- 设置 VIP 状态
function SetVIPStatus(identifier, isVIP)
    MySQL.Sync.execute("UPDATE users SET vip = @vip WHERE identifier = @identifier", {
        ['@vip'] = isVIP and 1 or 0,
        ['@identifier'] = identifier
    })
end

-- 设置 VIP 状态的命令
RegisterCommand('setvip', function(source, args)
    if IsPlayerAceAllowed(source, "admin.setvip") then
        local target = tonumber(args[1])
        local status = args[2] == "true"
        
        if target and status ~= nil then
            local identifier = GetPlayerIdentifier(target, 0)
            SetVIPStatus(identifier, status)
            TriggerClientEvent('chatMessage', source, "VIP状态已更新")
        else
            TriggerClientEvent('chatMessage', source, "用法: setvip <target_player_id> <true|false>")
        end
    else
        TriggerClientEvent('chatMessage', source, "你没有权限使用此命令")
    end
end)

-- 启动服务器时打印信息
AddEventHandler('onResourceStart', function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- print("VIP 管理系统已启动")
    end
end)


RegisterNetEvent('esx_hangarea:addStatus')
AddEventHandler('esx_hangarea:addStatus', function(status, value)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if xPlayer then
        -- 获取当前状态值
        TriggerEvent('esx_status:getStatus', _source, function(statuses)
            if statuses and statuses[status] then
                local currentValue = math.floor(statuses[status].getPercent())
                local maxValue = 100 -- esx_status 的最大值通常是100%

                -- 只有当前值小于最大值时才恢复
                if currentValue < maxValue then
                    -- 计算恢复后的值，确保不超过最大值
                    local newValue = math.min(currentValue + value, maxValue)
                    local addAmount = (newValue - currentValue) * 10000 -- esx_status 使用1000000为100%，所以1%=10000

                    if addAmount > 0 then
                        TriggerEvent('esx_status:add', _source, status, addAmount)
                        if Config.Debug and Config.Debug.Enabled then
                            print(string.format("玩家 %s 恢复 %s 状态: %d%% -> %d%% (+%d)", _source, status, currentValue, newValue, value))
                        end
                    end
                end
            else
                -- 如果无法获取当前状态，使用原来的方法
                TriggerEvent('esx_status:add', _source, status, value * 10000)
                if Config.Debug and Config.Debug.Enabled then
                    print(string.format("玩家 %s 恢复 %s 状态: +%d (直接添加)", _source, status, value))
                end
            end
        end)
    end
end)

-- 维持状态值在指定水平
RegisterNetEvent('esx_hangarea:maintainStatus')
AddEventHandler('esx_hangarea:maintainStatus', function(status, targetPercent)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if xPlayer then
        TriggerEvent('esx_status:getStatus', _source, function(statuses)
            if statuses and statuses[status] then
                local currentValue = math.floor(statuses[status].getPercent())

                -- 如果当前值低于目标值，则恢复到目标值
                if currentValue < targetPercent then
                    local addAmount = (targetPercent - currentValue) * 10000
                    TriggerEvent('esx_status:add', _source, status, addAmount)
                    if Config.Debug and Config.Debug.Enabled then
                        print(string.format("玩家 %s 维持 %s 状态: %d%% -> %d%% (+%d)", _source, status, currentValue, targetPercent, targetPercent - currentValue))
                    end
                end
            end
        end)
    end
end)

-- 直接设置状态值到指定百分比
RegisterNetEvent('esx_hangarea:setStatus')
AddEventHandler('esx_hangarea:setStatus', function(status, targetPercent)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if xPlayer then
        -- 先获取当前状态，然后计算需要添加的量
        TriggerEvent('esx_status:getStatus', _source, function(statuses)
            if statuses and statuses[status] then
                local currentValue = math.floor(statuses[status].getPercent())
                local difference = targetPercent - currentValue

                if difference > 0 then
                    -- 需要增加状态值
                    TriggerEvent('esx_status:add', _source, status, difference * 10000)
                    if Config.Debug and Config.Debug.Enabled then
                        print(string.format("玩家 %s 增加 %s 状态: %d%% -> %d%% (+%d)", _source, status, currentValue, targetPercent, difference))
                    end
                elseif difference < 0 then
                    -- 需要减少状态值（通常不会发生）
                    TriggerEvent('esx_status:remove', _source, status, math.abs(difference) * 10000)
                    if Config.Debug and Config.Debug.Enabled then
                        print(string.format("玩家 %s 减少 %s 状态: %d%% -> %d%% (%d)", _source, status, currentValue, targetPercent, difference))
                    end
                end
            else
                -- 如果无法获取状态，直接设置
                TriggerEvent('esx_status:set', _source, status, targetPercent * 10000)
                if Config.Debug and Config.Debug.Enabled then
                    print(string.format("玩家 %s 直接设置 %s 状态为: %d%%", _source, status, targetPercent))
                end
            end
        end)
    end
end)

-- 强制设置状态值（直接使用客户端事件）
RegisterNetEvent('esx_hangarea:forceSetStatus')
AddEventHandler('esx_hangarea:forceSetStatus', function(status, targetPercent)
    local _source = source
    local xPlayer = ESX.GetPlayerFromId(_source)

    if xPlayer then
        -- 直接触发客户端事件设置状态值
        local targetValue = targetPercent * 10000 -- 转换为esx_status的内部值
        TriggerClientEvent('esx_status:set', _source, status, targetValue)
        if Config.Debug and Config.Debug.Enabled then
            print(string.format("玩家 %s 强制设置 %s 状态为: %d%% (值: %d)", _source, status, targetPercent, targetValue))
        end
    end
end)
