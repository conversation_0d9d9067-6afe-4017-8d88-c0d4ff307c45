local ESX = exports['es_extended']:getSharedObject()

local blips = {}  -- 存储所有 Blip 的全局变量
local isInHangArea = false
local currentHangArea = nil
local isVIP = false
local rewardCountTotal = 0
local timeUntilNextReward = {} -- 用于存储每个区域的倒计时

-- 调试变量
local debugHunger = 0
local debugThirst = 0

-- 从配置中获取设置
local VIP_CHECK_INTERVAL = Config.VIPCheckInterval or 10000 -- 10秒检查一次
local RECOVERY_INTERVAL = Config.RecoverySettings.Interval or 5000 -- 状态恢复间隔（毫秒）
local RECOVERY_HEALTH_AMOUNT = Config.RecoverySettings.Health.Amount or 1 -- 每次恢复的健康值
local RECOVERY_ARMOR_AMOUNT = Config.RecoverySettings.Armor.Amount or 1 -- 每次恢复的护甲值

-- 检查是否在挂机区域
local function CheckHangArea()
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)

    for _, area in ipairs(Config.HangAreas) do
        local distance = #(playerCoords - area.coords)
        if distance <= area.radius then
            return true, area
        end
    end
    return false, nil
end

-- 更新NUI
local function UpdateNUIStatus()
    if isInHangArea and currentHangArea then
        SendNUIMessage({
            type = "updateHangAreaInfo",
            isInHangArea = true,
            isVIP = isVIP,
            rewardInterval = currentHangArea.rewardInterval,
            rewardCount = rewardCountTotal,
            remainingTime = timeUntilNextReward[currentHangArea.name] or 0
        })
    else
        -- 离开挂机区域时发送状态
        SendNUIMessage({
            type = "updateHangAreaInfo",
            isInHangArea = false
        })
    end
end

-- 主循环检测挂机区域
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(500)  -- 每500毫秒检测一次

        local inArea, area = CheckHangArea()
        
        -- 检测进入和离开区域的状态变化
        if inArea and not isInHangArea then
            -- 进入挂机区域
            isInHangArea = true
            currentHangArea = area
            timeUntilNextReward[area.name] = math.floor(area.rewardInterval / 1000)
            
            -- 触发进入事件和更新NUI
            TriggerServerEvent('hangarea:enterHangArea', area.name)
            UpdateNUIStatus()
        elseif not inArea and isInHangArea then
            -- 离开挂机区域
            isInHangArea = false
            currentHangArea = nil
            
            -- 重置相关状态
            for _, hangArea in ipairs(Config.HangAreas) do
                timeUntilNextReward[hangArea.name] = math.floor(hangArea.rewardInterval / 1000)
            end
            
            -- 更新NUI状态
            UpdateNUIStatus()
        end
    end
end)

-- 奖励倒计时
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)

        if isInHangArea and currentHangArea then
            local area = currentHangArea
            if timeUntilNextReward[area.name] and timeUntilNextReward[area.name] > 0 then
                timeUntilNextReward[area.name] = timeUntilNextReward[area.name] - 1
            end

            if timeUntilNextReward[area.name] == 0 then
                -- 触发奖励请求
                TriggerServerEvent('esx_hangarea:requestReward', area.name)
                -- 重置倒计时
                timeUntilNextReward[area.name] = area.rewardInterval / 1000
            end

            -- 实时更新NUI
            UpdateNUIStatus()
        end
    end
end)

-- 检查VIP状态
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(VIP_CHECK_INTERVAL)
        
        if isInHangArea then
            ESX.TriggerServerCallback('esx_hangarea:checkVIP', function(vipStatus)
                isVIP = vipStatus
                UpdateNUIStatus()
            end)
        end
    end
end)

-- 恢复状态
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(RECOVERY_INTERVAL)

        if isInHangArea then
            local playerPed = PlayerPedId()
            local playerHealth = GetEntityHealth(playerPed)
            local playerArmor = GetPedArmour(playerPed)

            -- 恢复健康
            if Config.RecoverySettings.Health.Enabled and playerHealth < Config.RecoverySettings.Health.MaxValue then
                SetEntityHealth(playerPed, math.min(playerHealth + RECOVERY_HEALTH_AMOUNT, Config.RecoverySettings.Health.MaxValue))
            end

            -- 恢复护甲
            if Config.RecoverySettings.Armor.Enabled and playerArmor < Config.RecoverySettings.Armor.MaxValue then
                SetPedArmour(playerPed, math.min(playerArmor + RECOVERY_ARMOR_AMOUNT, Config.RecoverySettings.Armor.MaxValue))
            end

            -- 通过服务器端恢复饱食度和口渴值
            if Config.RecoverySettings.Hunger.Enabled then
                TriggerServerEvent('esx_hangarea:addStatus', 'hunger', Config.RecoverySettings.Hunger.Amount)
                if Config.Debug and Config.Debug.Enabled then
                    print(string.format("客户端: 请求恢复饱食度 +%d", Config.RecoverySettings.Hunger.Amount))
                end
            end
            if Config.RecoverySettings.Thirst.Enabled then
                TriggerServerEvent('esx_hangarea:addStatus', 'thirst', Config.RecoverySettings.Thirst.Amount)
                if Config.Debug and Config.Debug.Enabled then
                    print(string.format("客户端: 请求恢复口渴值 +%d", Config.RecoverySettings.Thirst.Amount))
                end
            end
        end
    end
end)







-- 调试显示饱食度和口渴值 - 获取状态值
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(Config.Debug.UpdateInterval or 1000)

        if Config.Debug.Enabled and Config.Debug.ShowStatusValues then
            -- 获取当前状态值
            TriggerEvent('esx_status:getStatus', 'hunger', function(hunger)
                if hunger then
                    debugHunger = math.floor(hunger.getPercent())
                end
            end)

            TriggerEvent('esx_status:getStatus', 'thirst', function(thirst)
                if thirst then
                    debugThirst = math.floor(thirst.getPercent())
                end
            end)
        end
    end
end)

-- 调试显示饱食度和口渴值 - 绘制文本
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(0) -- 每帧绘制

        if Config.Debug.Enabled and Config.Debug.ShowStatusValues and isInHangArea then
            local debugText = string.format(
                "[挂机区域调试]\n饱食度: %d%%\n口渴值: %d%%\n区域: %s",
                debugHunger,
                debugThirst,
                currentHangArea and currentHangArea.name or "未知"
            )

            -- 显示在屏幕左上角
            SetTextFont(0)
            SetTextProportional(1)
            SetTextScale(0.4, 0.4)
            SetTextColour(255, 255, 255, 255)
            SetTextDropShadow(0, 0, 0, 0, 255)
            SetTextEdge(1, 0, 0, 0, 255)
            SetTextDropShadow()
            SetTextOutline()
            SetTextEntry("STRING")
            AddTextComponentString(debugText)
            DrawText(0.02, 0.02)
        end
    end
end)

-- 监听奖励事件更新奖励次数
RegisterNetEvent('esx_hangarea:updateRewardCount')
AddEventHandler('esx_hangarea:updateRewardCount', function(count)
    rewardCountTotal = count
    UpdateNUIStatus()
end)


-- 创建Blip函数
local function CreateHangAreaBlips()
    for _, area in ipairs(Config.HangAreas) do
        if area.blipVisible then
            local blip = AddBlipForCoord(area.coords.x, area.coords.y, area.coords.z)
            SetBlipSprite(blip, area.blipSprite)
            SetBlipColour(blip, area.blipColor)
            SetBlipAsShortRange(blip, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(area.name)
            EndTextCommandSetBlipName(blip)
            table.insert(blips, blip)
        end
    end
end

-- 在脚本初始化时调用
Citizen.CreateThread(function()
    CreateHangAreaBlips()
end)

-- 脚本结束时清理Blip
AddEventHandler('onResourceStop', function(resourceName)
    if (GetCurrentResourceName() == resourceName) then
        for _, blip in ipairs(blips) do
            RemoveBlip(blip)
        end
    end
end)

-- 调试命令：查看当前状态
RegisterCommand('checkstatus', function()
    if Config.Debug.Enabled then
        TriggerEvent('esx_status:getStatus', 'hunger', function(hunger)
            if hunger then
                local hungerPercent = math.floor(hunger.getPercent())
                TriggerEvent('chat:addMessage', {
                    color = {0, 255, 0},
                    multiline = true,
                    args = {"[调试]", "饱食度: " .. hungerPercent .. "%"}
                })
            end
        end)

        TriggerEvent('esx_status:getStatus', 'thirst', function(thirst)
            if thirst then
                local thirstPercent = math.floor(thirst.getPercent())
                TriggerEvent('chat:addMessage', {
                    color = {0, 255, 0},
                    multiline = true,
                    args = {"[调试]", "口渴值: " .. thirstPercent .. "%"}
                })
            end
        end)

        local statusText = isInHangArea and "在挂机区域内" or "不在挂机区域"
        TriggerEvent('chat:addMessage', {
            color = {0, 255, 0},
            multiline = true,
            args = {"[调试]", "挂机状态: " .. statusText}
        })
    else
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {"[错误]", "调试功能未启用"}
        })
    end
end, false)

-- 测试命令：手动恢复状态
RegisterCommand('testrecovery', function()
    if Config.Debug.Enabled then
        TriggerServerEvent('esx_hangarea:addStatus', 'hunger', 10)
        TriggerServerEvent('esx_hangarea:addStatus', 'thirst', 10)
        TriggerEvent('chat:addMessage', {
            color = {0, 255, 0},
            multiline = true,
            args = {"[调试]", "手动触发恢复: 饱食度+10, 口渴值+10"}
        })
    end
end, false)


