// 配置参数
const Config = {
    rewardInterval: 60000 // 默认值（毫秒）
};

let timeLeft = 0; 
let rewardCount = 0;
let isVIP = false;
let countdownInterval = null;

// 获取 DOM 元素
const uiContainer = document.getElementById('ui-container');
const timerDisplay = document.getElementById('timer');
const progressBar = document.getElementById('progress-bar');
const vipStatusDisplay = document.getElementById('vip-status');
const rewardCountDisplay = document.getElementById('reward-count');

// 格式化时间函数
const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainSeconds = seconds % 60;
    return `${String(minutes).padStart(2, '0')}:${String(remainSeconds).padStart(2, '0')}`;
};

// 更新 UI
const updateUI = () => {
    timerDisplay.textContent = formatTime(timeLeft);
    const percentage = ((Config.rewardInterval / 1000 - timeLeft) / (Config.rewardInterval / 1000)) * 100;
    progressBar.style.width = `${percentage}%`;
};

// 显示 UI
const showUI = () => {
    uiContainer.style.display = 'block';
};

// 隐藏 UI
const hideUI = () => {
    uiContainer.style.display = 'none';
    // 停止倒计时
    clearInterval(countdownInterval);
    // 重置状态
    timeLeft = 0;
    rewardCount = 0;
    isVIP = false;
};

// 倒计时主逻辑
const startCountdown = () => {
    clearInterval(countdownInterval);
    
    countdownInterval = setInterval(() => {
        if (timeLeft <= 0) {
            clearInterval(countdownInterval);
            rewardCount++;
            updateRewardCount();
            timeLeft = Math.floor(Config.rewardInterval / 1000);
            updateUI();
            startCountdown();
        } else {
            timeLeft--;
            updateUI();
        }
    }, 1000);
};

// 设置倒计时时间
const setTimeLeft = (time) => {
    timeLeft = time;
    updateUI();
    if (timeLeft > 0) {
        startCountdown();
    }
};

// VIP 状态更新
const setVIPStatus = (status) => {
    isVIP = status;
    vipStatusDisplay.textContent = isVIP ? "您是VIP用户" : "普通用户";
};

// 更新奖励数量
const updateRewardCount = () => {
    rewardCountDisplay.textContent = rewardCount;
};

// 监听 NUI 消息
window.addEventListener('message', function(event) {
    if (event.data.type === "updateHangAreaInfo") {
        // 检查是否在挂机区域
        if (event.data.isInHangArea) {
            // 显示 UI
            showUI();

            // 更新相关信息
            if (typeof event.data.rewardInterval === 'number') {
                Config.rewardInterval = event.data.rewardInterval;
                setTimeLeft(event.data.remainingTime || Math.floor(Config.rewardInterval / 1000));
            }
            setVIPStatus(event.data.isVIP || false);
            if (event.data.rewardCount !== undefined) {
                rewardCount = event.data.rewardCount;
                updateRewardCount();
            }
        } else {
            // 不在挂机区域，隐藏 UI
            hideUI();
        }
    }
});

// 页面加载初始化
document.addEventListener('DOMContentLoaded', () => {
    // 默认隐藏 UI
    hideUI();
});
