body {
    margin: 0;
    background: transparent; /* 确保背景透明 */
    font-family: Arial, sans-serif;
    overflow: hidden;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    background: rgba(0, 0, 0, 0.7); /* 半透明黑色背景 */
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.5);
    width: calc(25vw);
    max-width: 480px;
    color: white;
    z-index: 1000;
}

.title {
    font-size: 1.5em;
    color: #FFCC00; /* 亮眼的黄色 */
    margin-bottom: 10px;
}

.vip-status {
    font-size: 1.1em;
    color: #FFD700; /* 黄金色 */
    margin-bottom: 15px;
}

.message {
    margin-bottom: 10px;
    color: #ffffff; /* 纯白 */
}

.reward-status {
    margin-bottom: 15px;
    color: #ffffff; /* 纯白 */
}

.progress-container {
    background: rgba(255, 255, 255, 0.3); /* 更明显的半透明背景 */
    border-radius: 10px;
    height: 20px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: #CCFF00; /* 普通用户进度条颜色 */
    width: 0%;
    transition: width 0.5s linear;
}

#timer {
    color: #00FF66;  /* 倒计时颜色 */
    font-weight: bold;
}

/* VIP区特定样式 */
.vip-area .progress-bar {
    background-color: #FF0077; /* VIP进度条颜色 */
}

.vip-area .vip-status {
    color: #FF0077; /* VIP状态颜色 */
}

.vip-area .title {
    color: #FFD700; /* VIP标题颜色 */
}

.vip-area .area-type {
    background-color: #FF0077; /* VIP区域指示背景 */
    color: white;
    padding: 5px;
    border-radius: 5px;
}
